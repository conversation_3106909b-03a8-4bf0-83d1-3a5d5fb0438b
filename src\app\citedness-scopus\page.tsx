import { StaticContentPage } from "@/components/StaticContentPage";

export default function CitednessScopusPage() {
  return (
    <StaticContentPage title="CITEDNESS IN SCOPUS">
      <p>
        Jurnal Il<PERSON>h Ma<PERSON> (JIMEKA) sedang dalam proses pengajuan untuk diindeks di Scopus. Kami berkomitmen untuk memenuhi standar kualitas tinggi yang diperlukan untuk pengindeksan di basis data internasional terkemuka ini.
      </p>
      <p>
        Pengindeksan di Scopus akan secara signifikan meningkatkan visibilitas dan dampak sitasi artikel yang diterbitkan di JIMEKA, serta memberikan pengakuan internasional bagi para penulis kami.
      </p>
      <h3 className="text-2xl font-semibold mt-8">Proses Pengajuan:</h3>
      <p>
        Kami secara aktif bekerja untuk memenuhi semua kriteria yang ditetapkan oleh Scopus, termasuk kualitas konten, proses peer-review yang ketat, keberagaman dewan editorial, dan kepatuhan terhadap etika publikasi.
      </p>
      <p>
        Informasi lebih lanjut mengenai status pengajuan kami akan diperbarui di halaman ini. Kami berterima kasih atas dukungan dan kontribusi Anda dalam perjalanan kami menuju pengakuan global.
      </p>
    </StaticContentPage>
  );
}