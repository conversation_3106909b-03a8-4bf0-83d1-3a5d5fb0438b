@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%; /* Slightly off-white, very light grey-blue */
    --foreground: 222.2 47.4% 11.2%; /* Dark blue-grey */

    --card: 210 40% 98%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 210 40% 98%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --primary: 220.9 39.3% 39.3%; /* A professional, muted blue */
    --primary-foreground: 210 40% 98%; /* Light background for text on primary */
    --primary-hover: 220.9 39.3% 30%; /* Darker shade of primary for hover */

    --secondary: 210 40% 96.1%; /* Light grey */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%; /* Medium grey */

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 214.3 31.8% 91.4%; /* Light grey-blue border */
    --input: 214.3 31.8% 91.4%;
    --ring: 220.9 39.3% 39.3%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --radius: 0.75rem; /* Slightly larger radius for softer corners */

    --sidebar-background: 210 40% 96%; /* Slightly darker than main background, subtle distinction */
    --sidebar-foreground: 222.2 47.4% 11.2%;
    --sidebar-primary: 220.9 39.3% 39.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 94%; /* Slightly darker hover state */
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 88%; /* Slightly darker border */
    --sidebar-ring: 220.9 39.3% 39.3%;

    --jimeka-blue: 210 100% 66%; /* HSL for #52a9ff */
  }

  .dark {
    --background: 222.2 47.4% 11.2%; /* Dark blue-grey */
    --foreground: 210 40% 98%; /* Light background */

    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%; /* A vibrant blue for contrast */
    --primary-foreground: 0 0% 98%; /* Corrected: Light foreground for text on primary in dark mode */
    --primary-hover: 217.2 91.2% 45%; /* Darker shade of primary for hover in dark mode */

    --secondary: 217.2 32.6% 17.5%; /* Darker grey-blue */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%; /* Lighter grey */

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 217.2 32.6% 17.5%; /* Darker grey-blue border */
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --radius: 0.75rem;

    --sidebar-background: 222.2 47.4% 10%; /* Even darker, almost black-blue */
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 15%; /* Slightly lighter dark hover state */
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 15%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --jimeka-blue: 210 100% 66%; /* HSL for #52a9ff */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}